/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      // Custom spacing for Catalyst components
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      // Custom colors for the application
      colors: {
        // You can add custom colors here if needed
      },
      // Custom border radius
      borderRadius: {
        'lg': 'var(--radius-lg, 0.5rem)',
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
  ],
}
