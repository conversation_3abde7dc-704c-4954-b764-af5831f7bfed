import { test, expect } from '@playwright/test';

test.describe('InnerLoop App', () => {
  test('should load the application and show appropriate content', async ({ page }) => {
    // Listen for console messages
    page.on('console', msg => console.log('PAGE LOG:', msg.text()));
    page.on('pageerror', error => console.log('PAGE ERROR:', error.message));

    await page.goto('/');

    // Wait for the page to load
    await page.waitForLoadState('networkidle');

    // Check if the page title contains InnerLoop
    await expect(page).toHaveTitle(/InnerLoop/);

    // Take a screenshot for debugging
    await page.screenshot({ path: 'test-results/app-loaded.png' });

    // Check if the welcome message is visible (either loading, login, or authenticated state)
    const hasLoginWelcomeText = await page.locator('text=Welcome to InnerLoop').isVisible();
    const hasAuthenticatedWelcomeText = await page.locator('text=Welcome, User!').isVisible();
    const hasLoadingText = await page.locator('text=Loading...').isVisible();
    const hasAuthenticatedText = await page.locator('text=You are successfully authenticated with InnerLoop').isVisible();
    const hasSignInText = await page.locator('text=Please sign in to continue').isVisible();

    // Either loading, login welcome, sign in text, or authenticated state should be visible
    expect(hasLoginWelcomeText || hasAuthenticatedWelcomeText || hasLoadingText || hasAuthenticatedText || hasSignInText).toBe(true);

    // If authenticated, check for authenticated content
    if (hasAuthenticatedText || hasAuthenticatedWelcomeText) {
      await expect(page.locator('text=You are successfully authenticated with InnerLoop')).toBeVisible();
    }
    // If showing login form, check for sign in text and Descope component
    else if (hasLoginWelcomeText || hasSignInText) {
      await expect(page.locator('text=Please sign in to continue')).toBeVisible();
      // Note: Descope component might take time to load, so we'll check for its presence more flexibly
      const descopeComponent = page.locator('descope-wc');
      await expect(descopeComponent.or(page.locator('[data-testid="descope-wc"]'))).toBeVisible({ timeout: 10000 });
    }
  });

  test('should show loading state initially', async ({ page }) => {
    await page.goto('/');
    
    // Check if loading spinner appears initially
    const loadingText = page.locator('text=Loading...');
    
    // The loading text might appear briefly, so we'll check if it exists or if the main content is already loaded
    const isLoadingVisible = await loadingText.isVisible().catch(() => false);
    const isWelcomeVisible = await page.locator('text=Welcome to InnerLoop').isVisible().catch(() => false);
    
    // Either loading should be visible or the welcome message should be visible
    expect(isLoadingVisible || isWelcomeVisible).toBe(true);
  });
});
