import { test, expect } from '@playwright/test';

test.describe('Login Flow Tests', () => {
  test('should display login form and handle interaction attempts', async ({ page }) => {
    console.log('🧪 Testing complete login flow...');
    
    // Navigate to the app
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Verify page title
    await expect(page).toHaveTitle('InnerLoop');
    
    // Wait for Descope to load
    await page.waitForTimeout(5000);
    
    // Take initial screenshot
    await page.screenshot({ 
      path: 'test-results/login-flow-initial.png',
      fullPage: true 
    });
    
    // Verify login form elements are present
    await expect(page.locator('text=Welcome to InnerLoop')).toBeVisible();
    await expect(page.locator('text=Please sign in to continue')).toBeVisible();
    
    // Verify Descope component is loaded
    const descopeComponent = page.locator('descope-wc');
    await expect(descopeComponent).toBeVisible({ timeout: 15000 });
    
    console.log('✅ Descope component is visible');
    
    // Wait for Descope to fully initialize
    await page.waitForTimeout(3000);
    
    // Take screenshot after Descope loads
    await page.screenshot({ 
      path: 'test-results/login-flow-descope-loaded.png',
      fullPage: true 
    });
    
    // Try to find input fields in various ways
    // Descope might use shadow DOM or iframe
    
    // Method 1: Look for standard input fields
    const standardInputs = page.locator('input');
    const inputCount = await standardInputs.count();
    console.log(`Found ${inputCount} standard input fields`);
    
    // Method 2: Look for Descope-specific elements
    const descopeInputs = page.locator('descope-wc input, descope-wc [type="email"], descope-wc [type="password"]');
    const descopeInputCount = await descopeInputs.count();
    console.log(`Found ${descopeInputCount} Descope input fields`);
    
    // Method 3: Look for common form elements
    const formElements = page.locator('form, [role="form"], [data-testid*="form"], [class*="form"]');
    const formCount = await formElements.count();
    console.log(`Found ${formCount} form elements`);
    
    // Method 4: Look for buttons
    const buttons = page.locator('button, [role="button"], [type="submit"]');
    const buttonCount = await buttons.count();
    console.log(`Found ${buttonCount} button elements`);
    
    // Try to interact with any found elements
    if (inputCount > 0) {
      console.log('🎯 Attempting to interact with standard input fields');
      try {
        await standardInputs.first().click({ timeout: 5000 });
        await standardInputs.first().fill('<EMAIL>');
        console.log('✅ Successfully interacted with input field');
      } catch (error) {
        console.log('⚠️ Could not interact with standard input:', error.message);
      }
    }
    
    if (buttonCount > 0) {
      console.log('🎯 Found buttons - login form is interactive');
      try {
        const firstButton = buttons.first();
        const buttonText = await firstButton.textContent();
        console.log(`First button text: "${buttonText}"`);
        
        // Don't actually click submit buttons to avoid errors
        if (buttonText && !buttonText.toLowerCase().includes('submit') && !buttonText.toLowerCase().includes('sign in')) {
          await firstButton.click({ timeout: 5000 });
          console.log('✅ Successfully clicked button');
        }
      } catch (error) {
        console.log('⚠️ Could not interact with button:', error.message);
      }
    }
    
    // Take final screenshot
    await page.screenshot({ 
      path: 'test-results/login-flow-final.png',
      fullPage: true 
    });
    
    // Verify we're still on the login page (not authenticated)
    await expect(page.locator('text=Please sign in to continue')).toBeVisible();
    
    console.log('✅ Login flow test completed successfully!');
    console.log(`📊 Summary: ${inputCount} inputs, ${buttonCount} buttons, ${formCount} forms found`);
  });

  test('should handle page navigation and maintain login state', async ({ page }) => {
    console.log('🧪 Testing navigation and login state persistence...');
    
    // Navigate to the app
    await page.goto('/');
    await page.waitForLoadState('networkidle');
    
    // Verify we're on login page
    await expect(page.locator('text=Please sign in to continue')).toBeVisible();
    
    // Try navigating to a different path (should redirect back to login)
    await page.goto('/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Should still show login form since we're not authenticated
    await expect(page.locator('text=Please sign in to continue')).toBeVisible();
    
    // Take screenshot of redirect behavior
    await page.screenshot({ 
      path: 'test-results/login-redirect-test.png',
      fullPage: true 
    });
    
    console.log('✅ Navigation and login state test completed!');
  });
});
