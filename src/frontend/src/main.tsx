import React from 'react';
import ReactDOM from 'react-dom/client';
import { RouterProvider } from 'react-router-dom';
import { AuthProvider } from '@descope/react-sdk';
import { router } from './routes';
import './index.css';

console.log('main.tsx loading...');

const root = ReactDOM.createRoot(document.getElementById('root')!);

console.log('About to render App with router...');

root.render(
    <React.StrictMode>
        <AuthProvider projectId={import.meta.env.VITE_DESCOPE_PROJECT_ID || "P2vKC8jjsUAuunFbeEFzZmkcbsbb"}>
            <RouterProvider router={router} />
        </AuthProvider>
    </React.StrictMode>
);