import React from 'react';
import ReactDOM from 'react-dom/client';
import { AuthProvider } from '@descope/react-sdk';
import App from './App';
import './index.css';

console.log('main.tsx loading...');

const root = ReactDOM.createRoot(document.getElementById('root')!);

console.log('About to render App...');

root.render(
    <React.StrictMode>
        <AuthProvider projectId={import.meta.env.VITE_DESCOPE_PROJECT_ID || "P2vKC8jjsUAuunFbeEFzZmkcbsbb"}>
            <App />
        </AuthProvider>
    </React.StrictMode>
);