import React from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import App from '../App';
import { Layout } from '../components/layout/Layout';

// Lazy load pages for better performance
const Dashboard = React.lazy(() => import('../pages/Dashboard'));
const WorkflowList = React.lazy(() => import('../pages/WorkflowList'));
const WorkflowDetail = React.lazy(() => import('../pages/WorkflowDetail'));
const OpportunityList = React.lazy(() => import('../pages/OpportunityList'));
const OpportunityDetail = React.lazy(() => import('../pages/OpportunityDetail'));
const Settings = React.lazy(() => import('../pages/Settings'));
const Profile = React.lazy(() => import('../pages/Profile'));

/**
 * Application routes configuration
 */
export const router = createBrowserRouter([
  {
    path: '/',
    element: <App />,
    children: [
      {
        path: '/',
        element: <Layout />,
        children: [
          {
            index: true,
            element: <Navigate to="/dashboard" replace />,
          },
          {
            path: 'dashboard',
            element: <Dashboard />,
          },
          {
            path: 'workflows',
            children: [
              {
                index: true,
                element: <WorkflowList />,
              },
              {
                path: ':id',
                element: <WorkflowDetail />,
              },
            ],
          },
          {
            path: 'opportunities',
            children: [
              {
                index: true,
                element: <OpportunityList />,
              },
              {
                path: ':id',
                element: <OpportunityDetail />,
              },
            ],
          },
          {
            path: 'settings',
            element: <Settings />,
          },
          {
            path: 'profile',
            element: <Profile />,
          },
        ],
      },
    ],
  },
  {
    path: '*',
    element: <Navigate to="/dashboard" replace />,
  },
]);

/**
 * Route paths for easy reference throughout the app
 */
export const ROUTES = {
  DASHBOARD: '/dashboard',
  WORKFLOWS: '/workflows',
  WORKFLOW_DETAIL: (id: string) => `/workflows/${id}`,
  OPPORTUNITIES: '/opportunities',
  OPPORTUNITY_DETAIL: (id: string) => `/opportunities/${id}`,
  SETTINGS: '/settings',
  PROFILE: '/profile',
} as const;
