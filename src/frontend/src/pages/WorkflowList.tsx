import React from 'react';
import { Heading } from '../components/ui/heading';
import { Text } from '../components/ui/text';
import { Button } from '../components/ui/button';

export default function WorkflowList() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Heading level={1}>Workflows</Heading>
          <Text className="mt-2 text-gray-600">
            Manage and monitor your automated workflows
          </Text>
        </div>
        <Button color="indigo">
          Create Workflow
        </Button>
      </div>

      <div className="bg-white rounded-lg border border-gray-200 p-8 text-center">
        <Text className="text-gray-500">
          Workflow management interface will be implemented in Phase 1B
        </Text>
      </div>
    </div>
  );
}
