import React from 'react';
import { useAuth } from '../hooks/useAuth';
import { Heading } from '../components/ui/heading';
import { Text } from '../components/ui/text';

/**
 * Dashboard page - main landing page for authenticated users
 */
export default function Dashboard() {
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      <div>
        <Heading level={1}>Dashboard</Heading>
        <Text className="mt-2 text-gray-600">
          Welcome back, {user?.name || 'User'}! Here's what's happening with your workflows and opportunities.
        </Text>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-medium">W</span>
              </div>
            </div>
            <div className="ml-4">
              <Text className="text-sm font-medium text-gray-500">Active Workflows</Text>
              <Text className="text-2xl font-bold text-gray-900">12</Text>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-medium">O</span>
              </div>
            </div>
            <div className="ml-4">
              <Text className="text-sm font-medium text-gray-500">Open Opportunities</Text>
              <Text className="text-2xl font-bold text-gray-900">24</Text>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-medium">T</span>
              </div>
            </div>
            <div className="ml-4">
              <Text className="text-sm font-medium text-gray-500">Pending Tasks</Text>
              <Text className="text-2xl font-bold text-gray-900">8</Text>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
                <span className="text-white text-sm font-medium">$</span>
              </div>
            </div>
            <div className="ml-4">
              <Text className="text-sm font-medium text-gray-500">Revenue This Month</Text>
              <Text className="text-2xl font-bold text-gray-900">$45.2K</Text>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <Heading level={3}>Recent Activity</Heading>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <div>
                <Text className="text-sm font-medium text-gray-900">
                  New workflow "Lead Qualification" was created
                </Text>
                <Text className="text-xs text-gray-500">2 hours ago</Text>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <div>
                <Text className="text-sm font-medium text-gray-900">
                  Opportunity "Enterprise Deal" moved to "Negotiation"
                </Text>
                <Text className="text-xs text-gray-500">4 hours ago</Text>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <div>
                <Text className="text-sm font-medium text-gray-900">
                  Task "Follow up with client" was completed
                </Text>
                <Text className="text-xs text-gray-500">6 hours ago</Text>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
