@import "tailwindcss";

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* CSS custom properties for Catalyst components */
  --radius-lg: 0.5rem;
  --spacing: 1rem;

  /* Color scheme */
  color-scheme: light dark;

  /* Typography */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Base styles for the application */
body {
  margin: 0;
  min-height: 100vh;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: white;
  color: rgb(17 24 39); /* gray-900 */
}

/* Dark mode support - disabled for now to prevent dark-blue screen issue */
/*
@media (prefers-color-scheme: dark) {
  body {
    background-color: rgb(17 24 39);
    color: rgb(243 244 246);
  }
}
*/

/* Remove default button styles to let Catalyst handle them */
button {
  all: unset;
}

/* Focus styles for accessibility */
*:focus-visible {
  outline: 2px solid rgb(59 130 246); /* blue-500 */
  outline-offset: 2px;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(243 244 246); /* gray-100 */
}

::-webkit-scrollbar-thumb {
  background: rgb(156 163 175); /* gray-400 */
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128); /* gray-500 */
}

/* Dark mode scrollbar */
@media (prefers-color-scheme: dark) {
  ::-webkit-scrollbar-track {
    background: rgb(31 41 55); /* gray-800 */
  }

  ::-webkit-scrollbar-thumb {
    background: rgb(75 85 99); /* gray-600 */
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128); /* gray-500 */
  }
}
