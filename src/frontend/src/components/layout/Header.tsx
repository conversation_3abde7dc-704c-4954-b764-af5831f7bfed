import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Navbar, NavbarItem, NavbarSection, NavbarSpacer } from '../ui/navbar';
import { Dropdown, DropdownButton, DropdownItem, DropdownMenu } from '../ui/dropdown';
import { Avatar } from '../ui/avatar';
import { useAuth } from '../../hooks/useAuth';
import { ROUTES } from '../../routes';

/**
 * Header component with user menu and navigation
 * Uses Catalyst Navbar for consistent styling
 */
export function Header() {
  const { user, logout } = useAuth();
  const navigate = useNavigate();

  const handleProfileClick = () => {
    navigate(ROUTES.PROFILE);
  };

  const handleSettingsClick = () => {
    navigate(ROUTES.SETTINGS);
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <Navbar>
      <NavbarSection>
        <NavbarItem href={ROUTES.DASHBOARD} className="font-semibold text-lg">
          InnerLoop
        </NavbarItem>
      </NavbarSection>

      <NavbarSpacer />

      <NavbarSection>
        {/* User Menu */}
        <Dropdown>
          <DropdownButton as={NavbarItem}>
            <Avatar 
              src={undefined} // We can add avatar URL from user data later
              initials={user?.name?.split(' ').map(n => n[0]).join('') || 'U'}
              className="size-8"
            />
            <span className="ml-2 text-sm font-medium text-gray-700">
              {user?.name || 'User'}
            </span>
          </DropdownButton>
          <DropdownMenu className="min-w-64" anchor="bottom end">
            <DropdownItem onClick={handleProfileClick}>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Profile
              </span>
            </DropdownItem>
            <DropdownItem onClick={handleSettingsClick}>
              <span className="flex items-center">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Settings
              </span>
            </DropdownItem>
            <div className="border-t border-gray-100 my-1" />
            <DropdownItem onClick={handleLogout}>
              <span className="flex items-center text-red-600">
                <svg className="w-4 h-4 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Sign Out
              </span>
            </DropdownItem>
          </DropdownMenu>
        </Dropdown>
      </NavbarSection>
    </Navbar>
  );
}
